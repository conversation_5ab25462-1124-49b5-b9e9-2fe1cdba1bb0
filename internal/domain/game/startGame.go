package game

import (
	"context"
	"fmt"
	"math"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

func (s *service) StartGame(ctx context.Context, startGameInput *models.StartGameInput) (*models.Game, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	zlog.Debug(ctx, "Starting game", zap.String("userID", userID.Hex()), zap.Any("startGameInput", startGameInput))

	gameId := startGameInput.GameID
	if gameId == nil {
		return nil, fmt.Errorf("gameID is nil")
	}

	var game *models.Game
	game, err = s.GetGameByID(ctx, gameId)
	if err != nil {
		return nil, fmt.Errorf("game not found")
	}

	if game.GameType == models.GameTypeSumdayShowdown {
		return s.StartGameForShowdown(ctx, userID, *gameId)
	}
	if game.GameStatus == models.GameStatusStarted {
		zlog.Debug(ctx, "Game is already started", zap.String("gameID", gameId.Hex()))
		return game, nil
	}

	currPlayer := findPlayerByID(game.Players, userID)
	if currPlayer == nil {
		zlog.Debug(ctx, "current player found nil")
		return nil, fmt.Errorf("you cannot start the game as you are not a player of this game")
	}

	if game.GameType == models.GameTypeGroupPlay {
		game.GameStatus = models.GameStatusReady
	}

	if game.GameStatus != models.GameStatusReady {
		zlog.Info(ctx, "game cannot be started, current status is", zap.Any("gameStatus", game.GameStatus))
		return game, fmt.Errorf("game cannot be started, current status is %s", game.GameStatus)
	}

	game.GameStatus = models.GameStatusStarted

	game.LeaderBoard = make([]*models.LeaderBoardEntry, len(game.Players))
	for i, player := range game.Players {
		leaderboardEntry := gameutils.GetDefaultUserLeaderBoardStand(player.UserID)
		game.LeaderBoard[i] = &leaderboardEntry
	}

	if game.GameType != models.GameTypeFlashAnzan {
		var questionsForGame []*models.GameQuestion
		if game.GameType == models.GameTypeAbilityDuels {
			abilityQuestions, err := gameutils.GetAbilityQuestionsForGame(gameId.Hex(), game.Players, *game.Config, nil)
			if err != nil {
				zlog.Error(ctx, "failed to get ability questions", err)
				return nil, fmt.Errorf("failed to get ability questions: %w", err)
			}
			questionsForGame = abilityQuestions
		} else {
			questionsForGame = gameutils.GetQuestionsForGame(gameId.Hex(), game.Players, *game.Config)
		}

		if game.Config.MaxTimePerQuestion != nil && game.Config.TimeLimit != nil {
			maxQuestion := math.Ceil(float64(*game.Config.TimeLimit) / float64(*game.Config.MaxTimePerQuestion))
			questionsForGame = questionsForGame[0 : int(maxQuestion)+2]
			for _, que := range questionsForGame {
				que.Question.MaxTimeLimit = game.Config.MaxTimePerQuestion
			}
		}

		game.Questions = append(game.Questions, questionsForGame...)
		timeAfter5sec := time.Now().Add(5 * time.Second)
		game.StartTime = &timeAfter5sec

		err = s.addEncryptedQuestionsInGame(game)
		if err != nil {
			return nil, err
		}
	} else {
		game.StartTime = utils.AllocPtr(time.Now())
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_STARTED.String())
	if err != nil {
		return nil, err
	}

	ctx = context.WithValue(ctx, constants.GameIDKey, game.ID.Hex())

	s.scheduleEndGame(ctx, game)

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}
	var gameUsers []*models.User
	var botUser *models.User
	for _, player := range game.Players {
		user, err := s.userService.GetUserByID(ctx, player.UserID)
		if err != nil {
			return nil, err
		}
		gameUsers = append(gameUsers, user)
		if (user.IsBot != nil && *user.IsBot) || (user.IsHumanBot != nil && *user.IsHumanBot) {
			botUser = user
		}
	}

	go func() {
		err := botutils.RunBot(utils.DeriveContextWithoutCancel(ctx), game, botUser, s, s.cache, gameUsers)
		if err != nil {
			zlog.Error(ctx, "Error running bot after Accept Challenge", err)
		}
	}()

	return game, nil
}
