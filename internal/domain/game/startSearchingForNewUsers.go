package game

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/middleware"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

const (
	NEW_USER_BOT_WAIT_TIME = 3 * time.Second
)

// StartSearchingForNewUsers handles game searching for new users with less than 3 games played
func (s *service) StartSearchingForNewUsers(ctx context.Context, user *models.User, gameConfig models.GameConfig) (*bool, error) {
	zlog.Debug(ctx, "Starting search for new user", zap.String("userID", user.ID.Hex()))

	// Create a shorter timeout context for new users
	searchCtx, cancel := context.WithTimeout(utils.DeriveContextWithoutCancel(ctx), MAX_WAITING_TIME_FOR_USER)
	defer cancel()

	botMatchResultChan := make(chan *botMatchResult, 1)
	go s.handleNewUserSearchTimeout(searchCtx, user, gameConfig, botMatchResultChan)

	select {
	case res := <-botMatchResultChan:
		if res != nil {
			game, err := s.startGameWithBotForNewUsers(ctx, res.matchedUsers, gameConfig)
			if err != nil {
				zlog.Error(ctx, "Failed to start game with bot for new user", err)
				return nil, err
			}

			s.notifyMatchedPlayers(ctx, game, res.matchedUsers)
			s.scheduleEndGame(ctx, game)
		} else {
			zlog.Debug(ctx, "Game not found for new user, skipping notifications")
		}
	case <-ctx.Done():
		zlog.Debug(ctx, "Parent context cancelled for new user search")
		if err := s.coreService.PublishUserEvent(context.Background(), user.ID, &models.SearchTimeoutEvent{Event: constants.UserEventEnum.SEARCH_TIMEOUT.String()}); err != nil {
			zlog.Error(ctx, "Failed to publish search timeout event for new user", err)
		}
		return utils.AllocPtr(false), nil
	}

	success := true
	return &success, nil
}

func (s *service) startGameWithBotForNewUsers(ctx context.Context, matchedUsers []*models.User, gameConfig models.GameConfig) (*models.Game, error) {
	zlog.Debug(ctx, "Entering startGameWithBot", zap.Int("numUsers", len(matchedUsers)), zap.Any("gameConfig", gameConfig))

	players := s.getPlayersFromUsers(matchedUsers, gameConfig.GameType)
	zlog.Debug(ctx, "Players created", zap.Int("numPlayers", len(players)))

	game, err := s.createGameWithPlayers(ctx, players, gameConfig)
	if err != nil {
		zlog.Error(ctx, "Failed to create game with bot", err)
		return nil, fmt.Errorf("failed to create game with bot: %w", err)
	}
	zlog.Debug(ctx, "Game created successfully", zap.String("gameID", game.ID.Hex()))

	var botUser *models.User
	for _, u := range matchedUsers {
		if (u.IsHumanBot != nil && *u.IsHumanBot) || (u.IsBot != nil && *u.IsBot) {
			botUser = u
			break
		}
	}

	if botUser == nil {
		zlog.Debug(ctx, "No bot user found in matched users", zap.String("gameID", game.ID.Hex()))
		return nil, fmt.Errorf("no bot user found")
	}

	zlog.Debug(ctx, "Bot user identified", zap.String("botUserID", botUser.ID.Hex()), zap.Bool("isHumanBot", botUser.IsHumanBot != nil && *botUser.IsHumanBot))

	zlog.Debug(ctx, "Starting game with bot", zap.String("botUserID", botUser.ID.Hex()))

	// Run the bot
	go func() {
		traceCtx, span := middleware.WithSpan(utils.DeriveContextWithoutCancel(ctx), "startGameWithBot.runBot")
		defer span.End()
		zlog.Debug(traceCtx, "Starting bot goroutine", zap.String("botUserID", botUser.ID.Hex()))
		err := botutils.RunBot(traceCtx, game, botUser, s, s.cache, matchedUsers)
		if err != nil {
			zlog.Error(traceCtx, "Failed to run bot", err, zap.String("botUserID", botUser.ID.Hex()))
		}
		zlog.Debug(traceCtx, "Bot goroutine completed", zap.String("botUserID", botUser.ID.Hex()))
	}()

	zlog.Debug(ctx, "Exiting startGameWithBot", zap.String("gameID", game.ID.Hex()), zap.String("botUserID", botUser.ID.Hex()))
	return game, nil
}

func (s *service) handleNewUserSearchTimeout(ctx context.Context, user *models.User, gameConfig models.GameConfig, resultChan chan<- *botMatchResult) {
	zlog.Debug(ctx, "Entering handleNewUserSearchTimeout", zap.Any("gameConfig", gameConfig))

	// Use shorter wait time for new users
	botTimer := time.NewTimer(NEW_USER_BOT_WAIT_TIME)
	defer botTimer.Stop()

	zlog.Debug(ctx, "Starting new user bot timer", zap.Duration("duration", NEW_USER_BOT_WAIT_TIME))

	select {
	case <-botTimer.C:
		zlog.Debug(ctx, "New user bot timer completed")
		zlog.Debug(ctx, "Attempting to start game with human bot")
		res := s.handleHumanBotGameForNewUser(ctx, user, gameConfig)
		if res != nil {
			resultChan <- res
			zlog.Debug(ctx, "Exiting handleSearchTimeout after successful human bot match")
			return
		}
		zlog.Debug(ctx, "No suitable human bot found, proceeding with regular bot")
		zlog.Debug(ctx, "Attempting to handle regular bot game")
		res = s.handleBotGameForNewUsers(ctx, user, gameConfig, false)
		resultChan <- res
		zlog.Debug(ctx, "Bot game handling completed for new user",
			zap.Bool("matchFound", res != nil),
			zap.String("userID", user.ID.Hex()))
		return

	case <-ctx.Done():
		zlog.Debug(ctx, "Search context cancelled before bot attempt for new user", zap.Error(ctx.Err()))
		resultChan <- nil
		zlog.Debug(ctx, "Exiting handleNewUserSearchTimeout due to context cancellation")
		return
	}
}

// handleHumanBotGameForNewUser handles human bot matching for new users without checking the queue
func (s *service) handleHumanBotGameForNewUser(ctx context.Context, user *models.User, gameConfig models.GameConfig) *botMatchResult {
	zlog.Debug(ctx, "Entering handleHumanBotGameForNewUser", zap.Any("gameConfig", gameConfig))

	if gameConfig.GameType != models.GameTypePlayOnline {
		zlog.Debug(ctx, "Skipping human bot game")
		return nil
	}

	zlog.Debug(ctx, "Attempting to match human bot", zap.Int("userRating", *user.Rating))
	botUser, err := botutils.MatchHumanBot(ctx, s.userRepo, s.cache, *user.Rating, user.ID)
	if err != nil {
		zlog.Warn(ctx, "Failed to match human bot", zap.Error(err))
		return nil
	}

	if botUser == nil {
		zlog.Debug(ctx, "No suitable human bot found")
		return nil
	}

	zlog.Debug(ctx, "Human bot matched successfully", zap.String("botUserID", botUser.ID.Hex()))

	matchedUsers := []*models.User{user, botUser}

	if len(matchedUsers) != *gameConfig.NumPlayers {
		zlog.Warn(ctx, "Incorrect number of players for human bot game",
			zap.Int("matchedUsers", len(matchedUsers)),
			zap.Int("requiredPlayers", *gameConfig.NumPlayers))
		return nil
	}

	zlog.Debug(ctx, "Exiting handleHumanBotGameForNewUser with successful match", zap.String("botUserID", botUser.ID.Hex()))
	return &botMatchResult{matchedUsers}
}

func (s *service) handleBotGameForNewUsers(ctx context.Context, user *models.User, gameConfig models.GameConfig, isShadowBanned bool) *botMatchResult {
	zlog.Debug(ctx, "Entering handleBotGameForNewUsers", zap.Any("gameConfig", gameConfig))

	zlog.Debug(ctx, "User found in waiting list")

	zlog.Debug(ctx, "Attempting to get bot user")
	botUser, err := botutils.GetBotUser(ctx, user, s.userRepo, s.userService, gameConfig)
	if err != nil {
		zlog.Error(ctx, "Failed to get bot user", err)
		return nil
	}
	if botUser == nil {
		zlog.Debug(ctx, "No suitable bot user found")
		return nil
	}
	zlog.Debug(ctx, "Bot user found", zap.String("botUserID", botUser.ID.Hex()))

	matchedUsers := []*models.User{user, botUser}
	zlog.Debug(ctx, "Matched users", zap.String("botUserID", botUser.ID.Hex()))

	if len(matchedUsers) != *gameConfig.NumPlayers {
		zlog.Warn(ctx, "Incorrect number of players for bot game", zap.Int("matchedUsers", len(matchedUsers)), zap.Int("requiredPlayers", *gameConfig.NumPlayers))
		return nil
	}

	zlog.Debug(ctx, "Removing user from waiting list")
	_, err = s.playersQueue.RemoveUser(ctx, user.ID.Hex())
	if err != nil {
		zlog.Error(ctx, "Failed to remove user from queue", err)
		return nil
	}
	zlog.Debug(ctx, "User successfully removed from waiting list")

	zlog.Debug(ctx, "Exiting handleBotGame with successful match", zap.String("botUserID", botUser.ID.Hex()))
	return &botMatchResult{matchedUsers}
}
