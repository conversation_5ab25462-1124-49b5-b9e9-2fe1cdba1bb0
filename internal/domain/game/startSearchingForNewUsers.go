package game

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

const (
	NEW_USER_BOT_WAIT_TIME = 3 * time.Second
)

// StartSearchingForNewUsers handles game searching for new users with less than 3 games played
func (s *service) StartSearchingForNewUsers(ctx context.Context, user *models.User, gameConfig models.GameConfig) (*bool, error) {
	zlog.Debug(ctx, "Starting search for new user", zap.String("userID", user.ID.Hex()))

	// Create a shorter timeout context for new users
	searchCtx, cancel := context.WithTimeout(utils.DeriveContextWithoutCancel(ctx), MAX_WAITING_TIME_FOR_USER)
	defer cancel()

	botMatchResultChan := make(chan *bot<PERSON>atchResult, 1)
	go s.handleNewUserSearchTimeout(searchCtx, user, gameConfig, botMatchResultChan)

	select {
	case res := <-botMatchResultChan:
		if res != nil {
			game, err := s.startGameWithBot(ctx, res.matchedUsers, gameConfig)
			if err != nil {
				zlog.Error(ctx, "Failed to start game with bot for new user", err)
				return nil, err
			}

			s.notifyMatchedPlayers(ctx, game, res.matchedUsers)
			s.scheduleEndGame(ctx, game)
		} else {
			zlog.Debug(ctx, "Game not found for new user, skipping notifications")
		}
	case <-ctx.Done():
		zlog.Debug(ctx, "Parent context cancelled for new user search")
		if err := s.coreService.PublishUserEvent(context.Background(), user.ID, &models.SearchTimeoutEvent{Event: constants.UserEventEnum.SEARCH_TIMEOUT.String()}); err != nil {
			zlog.Error(ctx, "Failed to publish search timeout event for new user", err)
		}
		return utils.AllocPtr(false), nil
	}

	success := true
	return &success, nil
}

func (s *service) handleNewUserSearchTimeout(ctx context.Context, user *models.User, gameConfig models.GameConfig, resultChan chan<- *botMatchResult) {
	zlog.Debug(ctx, "Entering handleNewUserSearchTimeout", zap.Any("gameConfig", gameConfig))

	// Use shorter wait time for new users
	botTimer := time.NewTimer(NEW_USER_BOT_WAIT_TIME)
	defer botTimer.Stop()

	zlog.Debug(ctx, "Starting new user bot timer", zap.Duration("duration", NEW_USER_BOT_WAIT_TIME))

	select {
	case <-botTimer.C:
		zlog.Debug(ctx, "New user bot timer completed")
		zlog.Debug(ctx, "Attempting to start game with human bot")
		res := s.handleHumanBotGame(ctx, user, gameConfig)
		if res != nil {
			resultChan <- res
			zlog.Debug(ctx, "Exiting handleSearchTimeout after successful human bot match")
			return
		}
		zlog.Debug(ctx, "No suitable human bot found, proceeding with regular bot")
		zlog.Debug(ctx, "Attempting to handle regular bot game")
		res = s.handleBotGame(ctx, user, gameConfig, false)
		resultChan <- res
		zlog.Debug(ctx, "Bot game handling completed for new user",
			zap.Bool("matchFound", res != nil),
			zap.String("userID", user.ID.Hex()))
		return

	case <-ctx.Done():
		zlog.Debug(ctx, "Search context cancelled before bot attempt for new user", zap.Error(ctx.Err()))
		resultChan <- nil
		zlog.Debug(ctx, "Exiting handleNewUserSearchTimeout due to context cancellation")
		return
	}
}
